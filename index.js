// downloader.js - Complete Video Downloader with yt-dlp-wrap

import YTDlpWrap from 'yt-dlp-wrap';
import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import path from 'path';
import os from 'os';


// Main VideoDownloader class
class VideoDownloader extends EventEmitter {
    constructor(options = {}) {
        super();
        
        // Configuration with defaults
        this.config = {
            ytDlpPath: options.ytDlpPath || YTDlpWrap.getYtDlpPath(),
            outputPath: options.outputPath || path.join(os.homedir(), 'Downloads'),
            maxConcurrent: options.maxConcurrent || 3,
            retryAttempts: options.retryAttempts || 3,
            retryDelay: options.retryDelay || 1000,
            tempPath: options.tempPath || path.join(os.tmpdir(), 'video-downloader'),
            ...options
        };
        
        // Ensure output directory exists
        this.ensureDirectories();
        
        // Initialize yt-dlp wrapper
        this.ytDlpWrap = new YTDlpWrap(this.config.ytDlpPath);
        
        // Download queue management
        this.downloadQueue = new Map();
        this.activeDownloads = new Map();
        this.downloadHistory = new Map();
        
        // Performance tracking
        this.stats = {
            totalDownloads: 0,
            successfulDownloads: 0,
            failedDownloads: 0,
            totalBytesDownloaded: 0,
            averageSpeed: 0,
            sessionStartTime: Date.now()
        };
    }

    // Ensure required directories exist
    async ensureDirectories() {
        try {
            await fs.mkdir(this.config.outputPath, { recursive: true });
            await fs.mkdir(this.config.tempPath, { recursive: true });
        } catch (error) {
            console.error('Failed to create directories:', error);
        }
    }

    // Main download method with advanced options
    async download(url, options = {}) {
        const downloadId = this.generateDownloadId();
        const downloadOptions = this.buildDownloadOptions(options);
        
        const downloadTask = {
            id: downloadId,
            url,
            options: downloadOptions,
            status: 'queued',
            progress: 0,
            speed: 0,
            eta: null,
            startTime: null,
            endTime: null,
            error: null,
            retryCount: 0,
            fileSize: null,
            fileName: null,
            format: null
        };
        
        this.downloadQueue.set(downloadId, downloadTask);
        this.emit('download:queued', downloadTask);
        
        // Process queue
        setImmediate(() => this.processQueue());
        
        return downloadId;
    }

    // Build optimized download options
    buildDownloadOptions(userOptions) {
        const defaultOptions = {
            // Output options
            output: path.join(this.config.outputPath, '%(title)s.%(ext)s'),
            restrictFilenames: true,
            windowsFilenames: process.platform === 'win32',
            
            // Quality options
            format: userOptions.quality || 'bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best',
            mergeOutputFormat: userOptions.format || 'mp4',
            
            // Performance options
            concurrent: true,
            limitRate: userOptions.limitRate || null,
            bufferSize: '16K',
            httpChunkSize: '10M',
            
            // Network options
            retries: 10,
            fragmentRetries: 10,
            skipUnavailableFragments: true,
            socketTimeout: 30,
            
            // Progress tracking
            progress: true,
            newline: true,
            
            // Additional features
            writeSubtitles: userOptions.subtitles || false,
            writeAutoSubtitles: userOptions.autoSubtitles || false,
            subLang: userOptions.subtitleLang || 'en',
            writeThumbnail: userOptions.thumbnail || false,
            writeDescription: userOptions.description || false,
            writeInfoJson: userOptions.metadata || false,
            
            // Audio options
            extractAudio: userOptions.audioOnly || false,
            audioFormat: userOptions.audioFormat || 'mp3',
            audioQuality: userOptions.audioQuality || '192',
            
            // Playlist options
            noPlaylist: userOptions.noPlaylist !== false,
            playlistStart: userOptions.playlistStart || 1,
            playlistEnd: userOptions.playlistEnd || null,
            
            // Post-processing
            postprocessorArgs: [
                'ffmpeg:-c:v copy -c:a copy -threads 0 -preset fast'
            ],
            
            // Temp directory
            tempDir: this.config.tempPath,
            
            // Cookie support
            cookies: userOptions.cookieFile || null,
            
            // Age restriction bypass
            ageLimit: userOptions.ageLimit || null
        };
        
        // Remove null values
        Object.keys(defaultOptions).forEach(key => {
            if (defaultOptions[key] === null || defaultOptions[key] === undefined) {
                delete defaultOptions[key];
            }
        });
        
        return { ...defaultOptions, ...userOptions };
    }

    // Process download queue with concurrency control
    async processQueue() {
        while (this.downloadQueue.size > 0 && this.activeDownloads.size < this.config.maxConcurrent) {
            const [downloadId, task] = this.downloadQueue.entries().next().value;
            this.downloadQueue.delete(downloadId);
            this.activeDownloads.set(downloadId, task);
            
            // Start download asynchronously
            this.executeDownload(downloadId, task).catch(error => {
                console.error(`Download ${downloadId} failed:`, error);
                this.handleDownloadError(downloadId, task, error);
            });
        }
    }

    // Execute individual download with progress tracking
    async executeDownload(downloadId, task) {
        task.status = 'downloading';
        task.startTime = Date.now();
        this.emit('download:start', task);
        
        try {
            // Get video info first for better tracking
            const videoInfo = await this.getVideoInfo(task.url);
            task.fileName = videoInfo.title;
            task.fileSize = videoInfo.filesize;
            task.format = videoInfo.format;
            
            const args = [
                task.url,
                ...this.optionsToArgs(task.options)
            ];
            
            const ytDlpProcess = this.ytDlpWrap.exec(args);
            
            // Track process for cancellation
            task.process = ytDlpProcess;
            
            // Handle progress updates
            ytDlpProcess.on('progress', (progress) => {
                task.progress = progress.percent || 0;
                task.speed = this.parseSpeed(progress.speed);
                task.eta = progress.eta;
                
                // Update total bytes downloaded
                if (progress.downloaded_bytes) {
                    this.stats.totalBytesDownloaded += progress.downloaded_bytes - (task.lastBytes || 0);
                    task.lastBytes = progress.downloaded_bytes;
                }
                
                this.emit('download:progress', {
                    downloadId,
                    progress: task.progress,
                    speed: task.speed,
                    eta: task.eta,
                    downloaded: progress.downloaded_bytes,
                    total: progress.total_bytes || task.fileSize,
                    speedFormatted: this.formatSpeed(task.speed),
                    fileName: task.fileName
                });
            });
            
            // Handle stdout for additional info
            let outputBuffer = '';
            ytDlpProcess.on('ytDlpEvent', (eventType, eventData) => {
                outputBuffer += eventData;
                
                if (eventType === 'download' && eventData.includes('[download]')) {
                    this.parseDownloadInfo(eventData, task);
                }
                
                // Extract filename
                const filenameMatch = eventData.match(/\[download\] Destination: (.+)/);
                if (filenameMatch) {
                    task.fileName = path.basename(filenameMatch[1]);
                }

                // Extract format info
                const formatMatch = eventData.match(/\[info\] (.+): Downloading (.+)/);
                if (formatMatch) {
                    task.format = formatMatch[2];
                }
            });
            
            // Wait for completion
            await new Promise((resolve, reject) => {
                ytDlpProcess.on('close', (code) => {
                    if (code === 0) {
                        resolve();
                    } else {
                        reject(new Error(`Process exited with code ${code}`));
                    }
                });
                
                ytDlpProcess.on('error', (error) => {
                    reject(error);
                });
            });
            
            // Download completed successfully
            task.status = 'completed';
            task.endTime = Date.now();
            task.progress = 100;
            task.duration = task.endTime - task.startTime;
            
            this.updateStats(task);
            this.downloadHistory.set(downloadId, task);
            this.emit('download:complete', {
                ...task,
                duration: this.formatDuration(task.duration),
                averageSpeed: this.formatSpeed(task.fileSize / (task.duration / 1000))
            });
            
        } catch (error) {
            await this.handleDownloadError(downloadId, task, error);
        } finally {
            this.activeDownloads.delete(downloadId);
            delete task.process;
            delete task.lastBytes;
            setImmediate(() => this.processQueue());
        }
    }

    // Handle download errors with retry logic
    async handleDownloadError(downloadId, task, error) {
        task.error = error.message;
        task.status = 'failed';
        
        // Retry logic
        if (task.retryCount < this.config.retryAttempts) {
            task.retryCount++;
            task.status = 'retrying';
            task.error = null;
            this.emit('download:retry', {
                ...task,
                attempt: task.retryCount,
                maxAttempts: this.config.retryAttempts
            });
            
            await this.delay(this.config.retryDelay * task.retryCount);
            this.downloadQueue.set(downloadId, task);
            setImmediate(() => this.processQueue());
        } else {
            task.endTime = Date.now();
            this.stats.failedDownloads++;
            this.downloadHistory.set(downloadId, task);
            this.emit('download:failed', task);
        }
    }

    // Convert options object to command line arguments
    optionsToArgs(options) {
        const args = [];
        
        Object.entries(options).forEach(([key, value]) => {
            // Convert camelCase to kebab-case
            const argKey = `--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
            
            if (value === true) {
                args.push(argKey);
            } else if (value === false) {
                // Skip false boolean values
                return;
            } else if (Array.isArray(value)) {
                value.forEach(item => {
                    args.push(argKey, String(item));
                });
            } else if (value !== null && value !== undefined) {
                args.push(argKey, String(value));
            }
        });
        
        return args;
    }

    // Batch download multiple URLs
    async batchDownload(urls, options = {}) {
        const downloadIds = [];
        
        for (const url of urls) {
            const id = await this.download(url, options);
            downloadIds.push(id);
            
            // Small delay between queuing to prevent overwhelming
            await this.delay(100);
        }
        
        return downloadIds;
    }

    // Download playlist
    async downloadPlaylist(url, options = {}) {
        const playlistOptions = {
            ...options,
            noPlaylist: false
        };
        
        return this.download(url, playlistOptions);
    }

    // Get download status
    getDownloadStatus(downloadId) {
        return this.activeDownloads.get(downloadId) || 
               this.downloadQueue.get(downloadId) || 
               this.downloadHistory.get(downloadId) || 
               null;
    }

    // Get all active downloads
    getActiveDownloads() {
        return Array.from(this.activeDownloads.values());
    }

    // Get queued downloads
    getQueuedDownloads() {
        return Array.from(this.downloadQueue.values());
    }

    // Cancel download
    cancelDownload(downloadId) {
        const task = this.activeDownloads.get(downloadId) || this.downloadQueue.get(downloadId);
        
        if (!task) {
            return false;
        }
        
        if (task.process) {
            task.process.kill('SIGTERM');
        }
        
        task.status = 'cancelled';
        task.endTime = Date.now();
        this.activeDownloads.delete(downloadId);
        this.downloadQueue.delete(downloadId);
        this.downloadHistory.set(downloadId, task);
        
        this.emit('download:cancelled', task);
        setImmediate(() => this.processQueue());
        
        return true;
    }

    // Cancel all downloads
    cancelAllDownloads() {
        const allDownloads = [
            ...this.activeDownloads.keys(),
            ...this.downloadQueue.keys()
        ];
        
        allDownloads.forEach(downloadId => {
            this.cancelDownload(downloadId);
        });
        
        return allDownloads.length;
    }

    // Get video information without downloading
    async getVideoInfo(url) {
        try {
            const info = await this.ytDlpWrap.getVideoInfo(url);
            
            return {
                id: info.id,
                title: info.title || 'Unknown',
                duration: info.duration,
                uploader: info.uploader,
                description: info.description,
                thumbnail: info.thumbnail,
                formats: this.parseFormats(info.formats),
                filesize: info.filesize || info.filesize_approx,
                upload_date: info.upload_date,
                view_count: info.view_count,
                like_count: info.like_count,
                format: info.format,
                ext: info.ext,
                webpage_url: info.webpage_url,
                categories: info.categories,
                tags: info.tags,
                subtitles: info.subtitles ? Object.keys(info.subtitles) : [],
                is_live: info.is_live || false,
                was_live: info.was_live || false
            };
        } catch (error) {
            throw new Error(`Failed to get video info: ${error.message}`);
        }
    }

    // Search videos (requires yt-dlp with search support)
    async searchVideos(query, maxResults = 10) {
        try {
            const searchUrl = `ytsearch${maxResults}:${query}`;
            const info = await this.ytDlpWrap.getVideoInfo(searchUrl);
            
            if (info.entries) {
                return info.entries.map(entry => ({
                    id: entry.id,
                    title: entry.title,
                    url: entry.webpage_url || `https://www.youtube.com/watch?v=${entry.id}`,
                    duration: entry.duration,
                    uploader: entry.uploader,
                    thumbnail: entry.thumbnail,
                    view_count: entry.view_count,
                    upload_date: entry.upload_date
                }));
            }
            
            return [];
        } catch (error) {
            throw new Error(`Search failed: ${error.message}`);
        }
    }

    // Parse available formats
    parseFormats(formats) {
        if (!formats) return [];
        
        return formats
            .filter(format => format.url) // Only formats with URLs
            .map(format => ({
                format_id: format.format_id,
                ext: format.ext,
                quality: format.quality,
                filesize: format.filesize || format.filesize_approx,
                resolution: format.resolution || `${format.width || '?'}x${format.height || '?'}`,
                fps: format.fps,
                vcodec: format.vcodec,
                acodec: format.acodec,
                abr: format.abr,
                vbr: format.vbr,
                format_note: format.format_note,
                container: format.container,
                protocol: format.protocol
            }))
            .sort((a, b) => {
                // Sort by quality (height) first, then by filesize
                const aHeight = parseInt(a.resolution.split('x')[1]) || 0;
                const bHeight = parseInt(b.resolution.split('x')[1]) || 0;
                if (aHeight !== bHeight) return bHeight - aHeight;
                return (b.filesize || 0) - (a.filesize || 0);
            });
    }

    // Download with specific format
    async downloadFormat(url, formatId, options = {}) {
        const formatOptions = {
            ...options,
            format: formatId
        };
        
        return this.download(url, formatOptions);
    }

    // Extract audio only
    async extractAudio(url, options = {}) {
        const audioOptions = {
            ...options,
            extractAudio: true,
            audioFormat: options.audioFormat || 'mp3',
            audioQuality: options.audioQuality || '192',
            format: 'bestaudio/best'
        };
        
        return this.download(url, audioOptions);
    }

    // Update download statistics
    updateStats(task) {
        this.stats.totalDownloads++;
        this.stats.successfulDownloads++;
        
        const duration = (task.endTime - task.startTime) / 1000; // in seconds
        const speed = task.fileSize ? task.fileSize / duration : task.speed;
        
        // Update average speed
        this.stats.averageSpeed = 
            (this.stats.averageSpeed * (this.stats.successfulDownloads - 1) + speed) / 
            this.stats.successfulDownloads;
    }

    // Parse speed from progress string
    parseSpeed(speedStr) {
        if (!speedStr) return 0;
        
        const match = speedStr.match(/(\d+\.?\d*)\s*([KMGT]?)iB\/s/i);
        if (!match) return 0;
        
        const value = parseFloat(match[1]);
        const unit = match[2].toUpperCase();
        
        const multipliers = { '': 1, 'K': 1024, 'M': 1024**2, 'G': 1024**3, 'T': 1024**4 };
        
        return value * (multipliers[unit] || 1);
    }

    // Format speed for display
    formatSpeed(bytesPerSecond) {
        if (!bytesPerSecond) return '0 B/s';
        
        const units = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
        let unitIndex = 0;
        let speed = bytesPerSecond;
        
        while (speed >= 1024 && unitIndex < units.length - 1) {
            speed /= 1024;
            unitIndex++;
        }
        
        return `${speed.toFixed(2)} ${units[unitIndex]}`;
    }

    // Format duration for display
    formatDuration(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    }

    // Format file size
    formatFileSize(bytes) {
        if (!bytes) return 'Unknown';
        
        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let unitIndex = 0;
        let size = bytes;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return `${size.toFixed(2)} ${units[unitIndex]}`;
    }

    // Parse download information from stdout
    parseDownloadInfo(data, task) {
        // Parse progress percentage
        const progressMatch = data.match(/(\d+\.?\d*)%/);
        if (progressMatch) {
            task.progress = parseFloat(progressMatch[1]);
        }
        
        // Parse speed
        const speedMatch = data.match(/at\s+(\d+\.?\d*\s*[KMGT]?iB\/s)/i);
        if (speedMatch) {
            task.speed = this.parseSpeed(speedMatch[1]);
        }
        
        // Parse ETA
        const etaMatch = data.match(/ETA\s+(\d+:\d+)/);
        if (etaMatch) {
            task.eta = etaMatch[1];
        }
        
        // Parse file size
        const sizeMatch = data.match(/of\s+~?(\d+\.?\d*\s*[KMGT]?iB)/i);
        if (sizeMatch) {
            task.fileSize = this.parseSize(sizeMatch[1]);
        }
    }

    // Parse size string to bytes
    parseSize(sizeStr) {
        const match = sizeStr.match(/(\d+\.?\d*)\s*([KMGT]?)iB/i);
        if (!match) return 0;
        
        const value = parseFloat(match[1]);
        const unit = match[2].toUpperCase();
        
        const multipliers = { '': 1, 'K': 1024, 'M': 1024**2, 'G': 1024**3, 'T': 1024**4 };
        
        return value * (multipliers[unit] || 1);
    }

    // Utility methods
    generateDownloadId() {
        return `dl_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Get statistics
    getStats() {
        const sessionDuration = Date.now() - this.stats.sessionStartTime;
        
        return {
            ...this.stats,
            sessionDuration: this.formatDuration(sessionDuration),
            formattedAverageSpeed: this.formatSpeed(this.stats.averageSpeed),
            formattedTotalSize: this.formatFileSize(this.stats.totalBytesDownloaded),
            successRate: this.stats.totalDownloads > 0 
                ? ((this.stats.successfulDownloads / this.stats.totalDownloads) * 100).toFixed(2) + '%'
                : '0%'
        };
    }

    // Get download history
    getHistory(limit = 50) {
        const history = Array.from(this.downloadHistory.values())
            .sort((a, b) => (b.endTime || 0) - (a.endTime || 0))
            .slice(0, limit);
        
        return history.map(task => ({
            ...task,
            formattedDuration: task.duration ? this.formatDuration(task.duration) : 'N/A',
            formattedFileSize: this.formatFileSize(task.fileSize),
            formattedSpeed: this.formatSpeed(task.speed)
        }));
    }

    // Clear download history
    clearHistory() {
        this.downloadHistory.clear();
        this.emit('history:cleared');
    }

    // Export history to JSON
    async exportHistory(filePath) {
        const history = this.getHistory(Infinity);
        const data = JSON.stringify(history, null, 2);
        
        await fs.writeFile(filePath, data, 'utf8');
        return history.length;
    }

    // Import history from JSON
    async importHistory(filePath) {
        const data = await fs.readFile(filePath, 'utf8');
        const history = JSON.parse(data);
        
        history.forEach(task => {
            this.downloadHistory.set(task.id, task);
        });
        
        return history.length;
    }

    // Update yt-dlp binary
    async updateYtDlp() {
        try {
            this.emit('ytdlp:updating');
            await YTDlpWrap.downloadFromGithub();
            this.emit('ytdlp:updated');
            return true;
        } catch (error) {
            this.emit('ytdlp:update-failed', error);
            throw new Error(`Failed to update yt-dlp: ${error.message}`);
        }
    }

    // Check if yt-dlp is installed
    async checkYtDlp() {
        try {
            const version = await this.ytDlpWrap.getVersion();
            return {
                installed: true,
                version,
                path: this.config.ytDlpPath
            };
        } catch (error) {
            return {
                installed: false,
                error: error.message
            };
        }
    }

    // Clean temporary files
    async cleanTempFiles() {
        try {
            const files = await fs.readdir(this.config.tempPath);
            let cleaned = 0;
            
            for (const file of files) {
                const filePath = path.join(this.config.tempPath, file);
                const stats = await fs.stat(filePath);
                
                // Remove files older than 24 hours
                if (Date.now() - stats.mtime.getTime() > 24 * 60 * 60 * 1000) {
                    await fs.unlink(filePath);
                    cleaned++;
                }
            }
            
            return cleaned;
        } catch (error) {
            console.error('Failed to clean temp files:', error);
            return 0;
        }
    }
}

// Example usage and CLI interface
async function main() {
    // Create downloader instance
    const downloader = new VideoDownloader({
        outputPath: './downloads',
        maxConcurrent: 3
    });

    // Setup event listeners
    downloader.on('download:queued', (task) => {
        console.log(`📋 Queued: ${task.url}`);
    });

    downloader.on('download:start', (task) => {
        console.log(`🚀 Starting download: ${task.url}`);
    });

    downloader.on('download:progress', (progress) => {
        process.stdout.write(`\r⏬ Progress: ${progress.progress.toFixed(1)}% | Speed: ${progress.speedFormatted} | ETA: ${progress.eta || 'N/A'}`);
    });

    downloader.on('download:complete', (task) => {
        console.log(`\n✅ Completed: ${task.fileName} in ${task.duration}`);
    });

    downloader.on('download:failed', (task) => {
        console.error(`\n❌ Failed: ${task.url} - ${task.error}`);
    });

    downloader.on('download:retry', (task) => {
        console.log(`\n🔄 Retrying: ${task.url} (Attempt ${task.attempt}/${task.maxAttempts})`);
    });

    // Check if yt-dlp is installed
    const ytdlpCheck = await downloader.checkYtDlp();
    if (!ytdlpCheck.installed) {
        console.log('yt-dlp not found. Installing...');
        await downloader.updateYtDlp();
    } else {
        console.log(`yt-dlp version: ${ytdlpCheck.version}`);
    }

    // Example downloads
    try {
        // Single video download
        const videoUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
        
        // Get video info first
        console.log('\nFetching video info...');
        const info = await downloader.getVideoInfo(videoUrl);
        console.log(`Title: ${info.title}`);
        console.log(`Duration: ${info.duration}s`);
        console.log(`Available formats: ${info.formats.length}`);
        
        // Download video
        console.log('\nStarting download...');
        const downloadId = await downloader.download(videoUrl, {
            quality: 'best',
            subtitles: true,
            thumbnail: true
        });
        
        // Monitor download
        const checkStatus = setInterval(() => {
            const status = downloader.getDownloadStatus(downloadId);
            if (status && (status.status === 'completed' || status.status === 'failed')) {
                clearInterval(checkStatus);
                
                // Show statistics
                const stats = downloader.getStats();
                console.log('\n\n📊 Session Statistics:');
                console.log(`Total downloads: ${stats.totalDownloads}`);
                console.log(`Successful: ${stats.successfulDownloads}`);
                console.log(`Failed: ${stats.failedDownloads}`);
                console.log(`Success rate: ${stats.successRate}`);
                console.log(`Average speed: ${stats.formattedAverageSpeed}`);
                console.log(`Total downloaded: ${stats.formattedTotalSize}`);
            }
        }, 1000);
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}



    main().catch(console.error);
